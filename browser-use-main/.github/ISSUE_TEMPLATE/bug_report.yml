name: 🐛 Bug Report
description: Report a bug in browser-use
labels: ["bug", "triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! Please fill out the form below to help us reproduce and fix the issue.

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: When I try to... the library...
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: Reproduction Steps
      description: Steps to reproduce the behavior
      placeholder: |
        1. Install browser-use...
        2. Run the following task...
        3. See error...
    validations:
      required: true

  - type: textarea
    id: code
    attributes:
      label: Code Sample
      description: Include a minimal code sample that reproduces the issue
      render: python
    validations:
      required: true

  - type: input
    id: version
    attributes:
      label: Version
      description: What version of browser-use are you using? (Run `uv pip show browser-use` to find out)
      placeholder: "e.g., pip 0.1.26, or git main branch"
    validations:
      required: true

  - type: dropdown
    id: model
    attributes:
      label: LLM Model
      description: Which LLM model(s) are you using?
      multiple: true
      options:
        - GPT-4o
        - GPT-4
        - Claude 3.5 Sonnet
        - Claude 3.5 Opus
        - Claude 3.5 Haiku
        - Gemini 1.5 Pro
        - Gemini 1.5 Ultra
        - Fireworks Mixtral
        - DeepSeek Coder
        - Local Model (Specify model in description)
        - Other (specify in description)
    validations:
      required: true

  - type: input
    id: os
    attributes:
      label: Operating System
      description: What operating system are you using?
      placeholder: "e.g., macOS 13.1, Windows 11, Ubuntu 22.04"
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Relevant Log Output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code.
      render: shell
