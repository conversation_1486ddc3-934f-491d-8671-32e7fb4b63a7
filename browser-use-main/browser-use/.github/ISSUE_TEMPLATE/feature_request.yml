name: 💡 Feature Request
description: Suggest a new feature for browser-use
labels: ["enhancement"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to suggest a new feature! Please fill out the form below to help us understand your suggestion.

  - type: textarea
    id: problem
    attributes:
      label: Problem Description
      description: Is your feature request related to a problem? Please describe.
      placeholder: I'm always frustrated when...
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like to see
      placeholder: It would be great if...
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternative Solutions
      description: Describe any alternative solutions or features you've considered
      placeholder: I've also thought about...

  - type: textarea
    id: context
    attributes:
      label: Additional Context
      description: Add any other context or examples about the feature request here
      placeholder: |
        - Example use cases
        - Screenshots or mockups
        - Related issues or discussions
