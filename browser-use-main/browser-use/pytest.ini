[pytest]
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    asyncio: mark tests as async tests

testpaths =
    tests

python_files =
    test_*.py
    *_test.py

addopts =
    -v
    --strict-markers
    --tb=short

asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
log_cli = true
; log_cli_level = DEBUG
log_cli_format = %(levelname)-8s [%(name)s] %(message)s
filterwarnings =
    ignore::pytest.PytestDeprecationWarning
    ignore::DeprecationWarning

log_level = INFO
