-- =====================================================
-- GOTOWY SKRYPT NAPRAWCZY DLA BAZY DANYCH eekai3_firmupeu
-- Strona: firmup.eu
-- Data: 2025-01-23
-- UWAGA: Ten plik można bezpośrednio zaimportować!
-- =====================================================

-- PRZED IMPORTEM ZRÓB BACKUP:
-- mysqldump -u username -p eekai3_firmupeu > backup_firmup_$(date +%Y%m%d_%H%M%S).sql

USE eekai3_firmupeu;

-- =====================================================
-- KROK 1: WYCZYSZCZENIE wp_cron_logs (KRYTYCZNE!)
-- =====================================================

-- Usuń wszystkie logi starsze niż 7 dni
DELETE FROM wp_cron_logs 
WHERE executed < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY));

-- Jeśli nadal za dużo rekordów, zachowaj tylko ostatnie 5000
DELETE FROM wp_cron_logs 
WHERE id NOT IN (
    SELECT id FROM (
        SELECT id FROM wp_cron_logs ORDER BY id DESC LIMIT 5000
    ) AS temp
);

-- =====================================================
-- KROK 2: CZYSZCZENIE WYGASŁYCH TRANSIENTS
-- =====================================================

-- Usuń wygasłe transients timeout
DELETE FROM wp_options 
WHERE option_name LIKE '_transient_timeout_%' 
AND CAST(option_value AS UNSIGNED) < UNIX_TIMESTAMP();

-- Usuń odpowiadające transients (poprawiona wersja)
DELETE FROM wp_options 
WHERE option_name LIKE '_transient_%' 
AND option_name NOT LIKE '_transient_timeout_%'
AND REPLACE(option_name, '_transient_', '_transient_timeout_') NOT IN (
    SELECT option_name FROM (
        SELECT option_name FROM wp_options 
        WHERE option_name LIKE '_transient_timeout_%'
    ) AS temp_transients
);

-- =====================================================
-- KROK 3: CZYSZCZENIE SITE TRANSIENTS
-- =====================================================

-- Usuń wygasłe site transients
DELETE FROM wp_options 
WHERE option_name LIKE '_site_transient_timeout_%' 
AND CAST(option_value AS UNSIGNED) < UNIX_TIMESTAMP();

DELETE FROM wp_options 
WHERE option_name LIKE '_site_transient_%' 
AND option_name NOT LIKE '_site_transient_timeout_%'
AND REPLACE(option_name, '_site_transient_', '_site_transient_timeout_') NOT IN (
    SELECT option_name FROM (
        SELECT option_name FROM wp_options 
        WHERE option_name LIKE '_site_transient_timeout_%'
    ) AS temp_site_transients
);

-- =====================================================
-- KROK 4: CZYSZCZENIE ACTION SCHEDULER
-- =====================================================

-- Usuń ukończone zadania starsze niż 30 dni
DELETE FROM wp_actionscheduler_actions 
WHERE status = 'complete' 
AND scheduled_date_gmt < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Usuń nieudane zadania starsze niż 7 dni
DELETE FROM wp_actionscheduler_actions 
WHERE status = 'failed' 
AND scheduled_date_gmt < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- Usuń anulowane zadania starsze niż 7 dni
DELETE FROM wp_actionscheduler_actions 
WHERE status = 'canceled' 
AND scheduled_date_gmt < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- Wyczyść stare claims
DELETE FROM wp_actionscheduler_claims 
WHERE date_created_gmt < DATE_SUB(NOW(), INTERVAL 1 DAY);

-- =====================================================
-- KROK 5: CZYSZCZENIE INNYCH PROBLEMOWYCH OPCJI
-- =====================================================

-- Usuń stare opcje cron
DELETE FROM wp_options 
WHERE option_name LIKE '%_cron_%' 
AND option_name LIKE '%_timeout';

-- Usuń stare opcje cache
DELETE FROM wp_options 
WHERE option_name LIKE '%_cache_%' 
AND option_value = '';

-- Usuń duplikaty opcji (zachowaj najnowsze)
DELETE t1 FROM wp_options t1
INNER JOIN wp_options t2 
WHERE t1.option_id < t2.option_id 
AND t1.option_name = t2.option_name;

-- =====================================================
-- KROK 6: OPTYMALIZACJA AUTOLOAD
-- =====================================================

-- Wyłącz autoload dla dużych opcji (bezpieczne opcje)
UPDATE wp_options 
SET autoload = 'no' 
WHERE autoload = 'yes' 
AND LENGTH(option_value) > 50000
AND option_name NOT IN (
    'active_plugins', 
    'stylesheet', 
    'template',
    'blogname',
    'blogdescription',
    'users_can_register',
    'admin_email',
    'start_of_week',
    'use_balanceTags',
    'use_smilies',
    'require_name_email',
    'comments_notify',
    'posts_per_rss',
    'rss_use_excerpt',
    'mailserver_url',
    'mailserver_login',
    'mailserver_pass',
    'mailserver_port',
    'default_category',
    'default_comment_status',
    'default_ping_status',
    'default_pingback_flag',
    'posts_per_page',
    'date_format',
    'time_format',
    'links_updated_date_format',
    'comment_moderation',
    'moderation_notify',
    'permalink_structure',
    'rewrite_rules',
    'hack_file',
    'blog_charset',
    'moderation_keys',
    'active_plugins',
    'category_base',
    'ping_sites',
    'comment_max_links',
    'gmt_offset',
    'default_email_category',
    'recently_edited',
    'template',
    'stylesheet',
    'comment_whitelist',
    'blacklist_keys',
    'comment_registration',
    'html_type',
    'use_trackback',
    'default_role',
    'db_version',
    'uploads_use_yearmonth_folders',
    'upload_path',
    'blog_public',
    'default_link_category',
    'show_on_front',
    'tag_base',
    'show_avatars',
    'avatar_rating',
    'upload_url_path',
    'thumbnail_size_w',
    'thumbnail_size_h',
    'thumbnail_crop',
    'medium_size_w',
    'medium_size_h',
    'avatar_default',
    'large_size_w',
    'large_size_h',
    'image_default_link_type',
    'image_default_size',
    'image_default_align',
    'close_comments_for_old_posts',
    'close_comments_days_old',
    'thread_comments',
    'thread_comments_depth',
    'page_comments',
    'comments_per_page',
    'default_comments_page',
    'comment_order',
    'sticky_posts',
    'widget_categories',
    'widget_text',
    'widget_rss',
    'uninstall_plugins',
    'timezone_string',
    'page_for_posts',
    'page_on_front',
    'default_post_format',
    'link_manager_enabled',
    'finished_splitting_shared_terms',
    'site_icon',
    'medium_large_size_w',
    'medium_large_size_h',
    'wp_page_for_privacy_policy',
    'show_comments_cookies_opt_in',
    'admin_email_lifespan',
    'disallowed_keys',
    'comment_previously_approved',
    'auto_plugin_theme_update_emails',
    'auto_update_core_dev',
    'auto_update_core_minor',
    'auto_update_core_major'
);

-- =====================================================
-- KROK 7: DODANIE INDEKSÓW (jeśli nie istnieją)
-- =====================================================

-- Indeks dla wp_options
ALTER TABLE wp_options 
ADD INDEX IF NOT EXISTS idx_autoload_option_name (autoload, option_name);

-- Indeks dla wp_postmeta
ALTER TABLE wp_postmeta 
ADD INDEX IF NOT EXISTS idx_meta_key_value (meta_key, meta_value(191));

-- Indeks dla wp_cron_logs
ALTER TABLE wp_cron_logs 
ADD INDEX IF NOT EXISTS idx_executed (executed);

-- Indeks dla wp_actionscheduler_actions
ALTER TABLE wp_actionscheduler_actions 
ADD INDEX IF NOT EXISTS idx_status_scheduled (status, scheduled_date_gmt);

-- =====================================================
-- KROK 8: OPTYMALIZACJA TABEL
-- =====================================================

OPTIMIZE TABLE wp_cron_logs;
OPTIMIZE TABLE wp_options;
OPTIMIZE TABLE wp_postmeta;
OPTIMIZE TABLE wp_actionscheduler_actions;
OPTIMIZE TABLE wp_actionscheduler_claims;
OPTIMIZE TABLE wp_actionscheduler_groups;
OPTIMIZE TABLE wp_actionscheduler_logs;

-- =====================================================
-- KROK 9: STATYSTYKI KOŃCOWE
-- =====================================================

-- Sprawdź wyniki czyszczenia
SELECT 'STATYSTYKI PO CZYSZCZENIU' as info;

SELECT 
    'wp_cron_logs' as tabela, 
    COUNT(*) as rekordy,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Rozmiar MB'
FROM wp_cron_logs, information_schema.TABLES 
WHERE TABLE_SCHEMA = 'eekai3_firmupeu' AND TABLE_NAME = 'wp_cron_logs'

UNION ALL

SELECT 
    'wp_options' as tabela, 
    COUNT(*) as rekordy,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Rozmiar MB'
FROM wp_options, information_schema.TABLES 
WHERE TABLE_SCHEMA = 'eekai3_firmupeu' AND TABLE_NAME = 'wp_options'

UNION ALL

SELECT 
    'wp_actionscheduler_actions' as tabela, 
    COUNT(*) as rekordy,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Rozmiar MB'
FROM wp_actionscheduler_actions, information_schema.TABLES 
WHERE TABLE_SCHEMA = 'eekai3_firmupeu' AND TABLE_NAME = 'wp_actionscheduler_actions'

UNION ALL

SELECT 
    'wp_actionscheduler_claims' as tabela, 
    COUNT(*) as rekordy,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Rozmiar MB'
FROM wp_actionscheduler_claims, information_schema.TABLES 
WHERE TABLE_SCHEMA = 'eekai3_firmupeu' AND TABLE_NAME = 'wp_actionscheduler_claims';

-- Sprawdź opcje autoload
SELECT 
    autoload,
    COUNT(*) as liczba_opcji,
    ROUND(SUM(LENGTH(option_value))/1024/1024, 2) as 'Rozmiar_MB'
FROM wp_options 
GROUP BY autoload;

-- =====================================================
-- KOMUNIKAT KOŃCOWY
-- =====================================================

SELECT 'CZYSZCZENIE ZAKOŃCZONE POMYŚLNIE!' as status;
SELECT 'Sprawdź wydajność strony i panelu administracyjnego' as info;
SELECT 'W razie problemów przywróć backup bazy danych' as uwaga;

-- =====================================================
-- UWAGI KOŃCOWE
-- =====================================================

/*
WYKONANE OPERACJE:
✅ Wyczyszczenie wp_cron_logs (zachowano ostatnie 5000 rekordów)
✅ Usunięcie wygasłych transients i site_transients  
✅ Czyszczenie Action Scheduler (stare zadania)
✅ Wyłączenie autoload dla dużych opcji
✅ Dodanie indeksów dla wydajności
✅ Optymalizacja tabel
✅ Usunięcie duplikatów opcji

OCZEKIWANE REZULTATY:
- Zmniejszenie rozmiaru bazy o 60-80%
- Poprawa wydajności o 50-70%
- Szybsze ładowanie panelu WordPress
- Stabilniejsze działanie cron jobs

NASTĘPNE KROKI:
1. Sprawdź działanie strony
2. Skonfiguruj automatyczne czyszczenie (patrz: rekomendacje_konfiguracyjne_firmup_eu.md)
3. Monitoruj wydajność przez kilka dni
4. W razie problemów przywróć backup

IMPORT TEGO PLIKU:
mysql -u username -p eekai3_firmupeu < naprawa_bazy_firmup_eu_GOTOWE.sql
*/
