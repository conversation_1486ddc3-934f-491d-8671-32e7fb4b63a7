<?php
//Begin Really Simple SSL key
define('RSSSL_KEY', 'vEr6jF4O7TgnVuhOFKYf92kNiNw4xX5GrPvvUv6pEjGHyRriktl4Mx1bHGSCapDs');
//END Really Simple SSL key

//Begin Really Simple SSL session cookie settings
@ini_set('session.cookie_httponly', true);
@ini_set('session.cookie_secure', true);
@ini_set('session.use_only_cookies', true);
//END Really Simple SSL cookie settings

define('WP_CACHE', true);

/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the
 * installation. You don't have to use the web site, you can
 * copy this file to "wp-config.php" and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * MySQL settings
 * * Secret keys
 * * Database table prefix
 * * ABSPATH
 *
 * @link https://codex.wordpress.org/Editing_wp-config.php
 *
 * @package WordPress
 */

// ** MySQL settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define('DB_NAME', 'eekai3_firmupeu');

/** MySQL database username */
define('DB_USER', 'saich9_firmupeu');

/** MySQL database password */
define('DB_PASSWORD', 'oth4ciMa5sho');

/** MySQL hostname */
define('DB_HOST', 'mediaplanet.mysql.dhosting.pl');

/** Database Charset to use in creating database tables. */
define('DB_CHARSET', 'utf8mb4');

/** The Database Collate type. Don't change this if in doubt. */
define('DB_COLLATE', '');

/**#@+
 * Authentication Unique Keys and Salts.
 *
 * Change these to different unique phrases!
 * You can generate these using the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}
 * You can change these at any point in time to invalidate all existing cookies. This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define('AUTH_KEY',         '3U;+++V$>C7zq.U3{VC1RU95XOtRjIxB*aT.ut7okd)2P+;TbW:a7f{^pD1u#kUu');
define('SECURE_AUTH_KEY',  '{G=^HDwnY:vioJu)&N/tL>1M>b0%{G!>V#aRVkw:S0]`PiTls>WdC9p0yU1NTip@');
define('LOGGED_IN_KEY',    '89~]i 3o,z#B->=FdjN l1f94w0s<p_ga^QegMuifh{l4^6O)U=q+?}rl+I=.+T#');
define('NONCE_KEY',        '{fi-pjq)&M,EUzD$sX.k9(;:D=}B|:,=*F^24t7zBdsArF]&5zh,..85: (H?B]D');
define('AUTH_SALT',        'Z68X763WhaFxJ~%1pAu4^`<,io8vY|=VF!o@IEgI:`R@(zfaKvuI~m^mk9n)M6ku');
define('SECURE_AUTH_SALT', 'FTG1yUF=3W(MGFq:6s&[4rZ4%n/F>dt?h`0RIh9GJO#U8@8Y*Ol6nS~A11(rMp`V');
define('LOGGED_IN_SALT',   '4WAJJ$15ULu$_F;B+v}[OpIE#<ChX34bP|wAfg1tfSNXqR$F!^][{hlz:Nl_x/l`');
define('NONCE_SALT',       '^V@T70T&xM9)(5(&cJ=^#zk&bBI-*2z?AI]V=Crp_rF~X3LR1x`]+vH/TjOy<*}`');

/**#@-*/

/**
 * WordPress Database Table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 */
$table_prefix  = 'wp_';

/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the Codex.
 *
 * @link https://codex.wordpress.org/Debugging_in_WordPress
 */
define( 'WP_DEBUG', true );
define( 'WP_DEBUG_LOG', true );
define( 'WP_DEBUG_DISPLAY', false );

define('DISABLE_WP_CRON', true);

/* That's all, stop editing! Happy blogging. */

/** Absolute path to the WordPress directory. */
if ( !defined('ABSPATH') )
	define('ABSPATH', dirname(__FILE__) . '/');

/** Sets up WordPress vars and included files. */
require_once(ABSPATH . 'wp-settings.php');
