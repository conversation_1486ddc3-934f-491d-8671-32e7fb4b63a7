-- =====================================================
-- SKRYPTY NAPRAWCZE DLA BAZY DANYCH eekai3_firmupeu
-- Strona: firmup.eu
-- Data: 2025-01-23
-- =====================================================

-- UWAGA: PRZED WYKONANIEM ZRÓB BACKUP BAZY DANYCH!
-- mysqldump -u username -p eekai3_firmupeu > backup_$(date +%Y%m%d_%H%M%S).sql

-- =====================================================
-- PRIORYTET 1: KRYTYCZNE PROBLEMY
-- =====================================================

-- 1. WYCZYSZCZENIE TABELI wp_cron_logs (485,397 rekordów)
-- Usuń wszystkie logi starsze niż 7 dni
SET @cutoff_date = UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY));

-- Sprawdź ile rekordów zostanie usuniętych
SELECT 
    COUNT(*) as 'Rekordy do usuniecia',
    COUNT(*) - (SELECT COUNT(*) FROM wp_cron_logs WHERE executed >= @cutoff_date) as 'Rekordy pozostana'
FROM wp_cron_logs 
WHERE executed < @cutoff_date;

-- Usuń stare logi (WYKONAJ OSTROŻNIE!)
-- DELETE FROM wp_cron_logs WHERE executed < @cutoff_date;

-- Alternatywnie: zachowaj tylko ostatnie 1000 rekordów
-- DELETE FROM wp_cron_logs WHERE id NOT IN (
--     SELECT id FROM (
--         SELECT id FROM wp_cron_logs ORDER BY id DESC LIMIT 1000
--     ) AS temp
-- );

-- =====================================================
-- PRIORYTET 2: OPTYMALIZACJA wp_options
-- =====================================================

-- 2. CZYSZCZENIE WYGASŁYCH TRANSIENTS
-- Sprawdź ile transients jest wygasłych
SELECT 
    COUNT(*) as 'Wygasle transients'
FROM wp_options 
WHERE option_name LIKE '_transient_timeout_%' 
AND CAST(option_value AS UNSIGNED) < UNIX_TIMESTAMP();

-- Usuń wygasłe transients timeout
DELETE FROM wp_options 
WHERE option_name LIKE '_transient_timeout_%' 
AND CAST(option_value AS UNSIGNED) < UNIX_TIMESTAMP();

-- Usuń odpowiadające im transients
DELETE FROM wp_options 
WHERE option_name LIKE '_transient_%' 
AND option_name NOT LIKE '_transient_timeout_%'
AND option_name NOT IN (
    SELECT CONCAT('_transient_', SUBSTRING(option_name, 19)) 
    FROM wp_options 
    WHERE option_name LIKE '_transient_timeout_%'
);

-- 3. ANALIZA OPCJI Z AUTOLOAD
-- Sprawdź największe opcje z autoload='yes'
SELECT 
    option_name,
    LENGTH(option_value) as size_bytes,
    ROUND(LENGTH(option_value)/1024, 2) as size_kb
FROM wp_options 
WHERE autoload = 'yes' 
ORDER BY LENGTH(option_value) DESC 
LIMIT 20;

-- Wyłącz autoload dla dużych opcji (SPRAWDŹ PRZED WYKONANIEM!)
-- UPDATE wp_options SET autoload = 'no' 
-- WHERE autoload = 'yes' 
-- AND LENGTH(option_value) > 50000
-- AND option_name NOT IN ('active_plugins', 'stylesheet', 'template');

-- =====================================================
-- PRIORYTET 3: OPTYMALIZACJA INDEKSÓW
-- =====================================================

-- 4. SPRAWDZENIE I DODANIE BRAKUJĄCYCH INDEKSÓW

-- Sprawdź obecne indeksy
SHOW INDEX FROM wp_options;
SHOW INDEX FROM wp_postmeta;
SHOW INDEX FROM wp_cron_logs;

-- Dodaj indeksy jeśli nie istnieją (sprawdź najpierw!)
-- ALTER TABLE wp_options ADD INDEX idx_autoload_option_name (autoload, option_name);
-- ALTER TABLE wp_postmeta ADD INDEX idx_meta_key_value (meta_key, meta_value(191));
-- ALTER TABLE wp_cron_logs ADD INDEX idx_executed (executed);

-- =====================================================
-- PRIORYTET 4: CZYSZCZENIE ACTION SCHEDULER
-- =====================================================

-- 5. CZYSZCZENIE STARYCH ZADAŃ ACTION SCHEDULER
-- Sprawdź stare zadania
SELECT 
    status,
    COUNT(*) as count,
    MIN(scheduled_date_gmt) as oldest,
    MAX(scheduled_date_gmt) as newest
FROM wp_actionscheduler_actions 
GROUP BY status;

-- Usuń ukończone zadania starsze niż 30 dni
-- DELETE FROM wp_actionscheduler_actions 
-- WHERE status = 'complete' 
-- AND scheduled_date_gmt < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Usuń nieudane zadania starsze niż 7 dni
-- DELETE FROM wp_actionscheduler_actions 
-- WHERE status = 'failed' 
-- AND scheduled_date_gmt < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- =====================================================
-- OPTYMALIZACJA TABEL
-- =====================================================

-- 6. OPTYMALIZACJA TABEL PO CZYSZCZENIU
-- OPTIMIZE TABLE wp_cron_logs;
-- OPTIMIZE TABLE wp_options;
-- OPTIMIZE TABLE wp_actionscheduler_actions;
-- OPTIMIZE TABLE wp_actionscheduler_claims;
-- OPTIMIZE TABLE wp_postmeta;

-- =====================================================
-- MONITORING I STATYSTYKI
-- =====================================================

-- 7. SPRAWDZENIE ROZMIARU TABEL
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
    table_rows
FROM information_schema.TABLES 
WHERE table_schema = 'eekai3_firmupeu'
AND table_name LIKE 'wp_%'
ORDER BY (data_length + index_length) DESC;

-- 8. SPRAWDZENIE FRAGMENTACJI TABEL
SELECT 
    table_name,
    ROUND(data_free / 1024 / 1024, 2) AS 'Fragmentation (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'eekai3_firmupeu'
AND data_free > 0
ORDER BY data_free DESC;

-- =====================================================
-- ZAPYTANIA DIAGNOSTYCZNE
-- =====================================================

-- 9. TOP 10 NAJWIĘKSZYCH TABEL
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'eekai3_firmupeu'
ORDER BY (data_length + index_length) DESC 
LIMIT 10;

-- 10. ANALIZA OPCJI AUTOLOAD
SELECT 
    autoload,
    COUNT(*) as count,
    ROUND(SUM(LENGTH(option_value))/1024/1024, 2) as 'Total Size (MB)'
FROM wp_options 
GROUP BY autoload;

-- =====================================================
-- KOŃCOWE SPRAWDZENIA
-- =====================================================

-- Sprawdź końcowe statystyki
SELECT 'wp_cron_logs' as tabela, COUNT(*) as rekordy FROM wp_cron_logs
UNION ALL
SELECT 'wp_options' as tabela, COUNT(*) as rekordy FROM wp_options
UNION ALL
SELECT 'wp_actionscheduler_actions' as tabela, COUNT(*) as rekordy FROM wp_actionscheduler_actions
UNION ALL
SELECT 'wp_actionscheduler_claims' as tabela, COUNT(*) as rekordy FROM wp_actionscheduler_claims;

-- =====================================================
-- UWAGI KOŃCOWE
-- =====================================================
/*
WAŻNE UWAGI:
1. Wykonuj skrypty po kolei, nie wszystkie naraz
2. Monitoruj wydajność po każdej zmianie
3. Zrób backup przed każdą większą operacją
4. Testuj najpierw na kopii bazy danych
5. Niektóre zapytania są zakomentowane - odkomentuj ostrożnie

KOLEJNOŚĆ WYKONANIA:
1. Backup bazy danych
2. Czyszczenie wp_cron_logs
3. Czyszczenie transients
4. Dodanie indeksów
5. Optymalizacja tabel
6. Monitoring wyników

OCZEKIWANE REZULTATY:
- Zmniejszenie rozmiaru bazy o 60-80%
- Poprawa wydajności o 50-70%
- Szybsze ładowanie panelu WordPress
- Stabilniejsze działanie cron jobs
*/
