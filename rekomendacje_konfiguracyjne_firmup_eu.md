# Rekomendacje Konfiguracyjne dla firmup.eu

## Konfiguracja WordPress (wp-config.php)

### 1. Optymalizacja Bazy <PERSON>
```php
// Zwięks<PERSON> limit pamięci
define('WP_MEMORY_LIMIT', '512M');

// Optymalizacja bazy danych
define('WP_AUTO_UPDATE_CORE', false);
define('AUTOMATIC_UPDATER_DISABLED', true);

// Ograniczenie rewizji postów
define('WP_POST_REVISIONS', 3);
define('AUTOSAVE_INTERVAL', 300); // 5 minut

// Optymalizacja śmietnika
define('EMPTY_TRASH_DAYS', 7); // Usuń po 7 dniach zamiast 30
```

### 2. Konfiguracja Cache
```php
// Włącz cache obiektów
define('WP_CACHE', true);

// Cache dla zapytań bazy danych
define('WP_CACHE_KEY_SALT', 'firmup_eu_');
```

### 3. Konfiguracja <PERSON>
```php
// Wyłącz domyślny cron WordPress (użyj system cron)
define('DISABLE_WP_CRON', true);

// Alternatywnie: ograniczenie cron
define('WP_CRON_LOCK_TIMEOUT', 60);
```

## Konfiguracja Pluginów

### 1. Cron Logger - WYŁĄCZ lub OGRANICZENIE
```php
// W functions.php motywu lub plugin
add_filter('cron_logger_enabled', '__return_false');

// Lub ograniczenie logowania
add_filter('cron_logger_log_level', function() {
    return 'error'; // Loguj tylko błędy
});
```

### 2. Action Scheduler - Optymalizacja
```php
// Ograniczenie zadań Action Scheduler
add_filter('action_scheduler_queue_runner_batch_size', function() {
    return 10; // Zmniejsz z domyślnych 25
});

add_filter('action_scheduler_queue_runner_time_limit', function() {
    return 20; // Zmniejsz czas wykonania
});

// Automatyczne czyszczenie
add_filter('action_scheduler_retention_period', function() {
    return DAY_IN_SECONDS * 7; // Zachowaj tylko 7 dni
});
```

### 3. WooCommerce - Optymalizacja
```php
// Ograniczenie zadań WooCommerce
add_filter('woocommerce_background_image_regeneration', '__return_false');
add_filter('woocommerce_queue_batch_size', function() { return 5; });

// Wyłącz niepotrzebne funkcje
add_filter('woocommerce_enable_setup_wizard', '__return_false');
add_filter('woocommerce_show_admin_notice', '__return_false');
```

### 4. Jetpack - Ograniczenia
```php
// Ograniczenie synchronizacji Jetpack
add_filter('jetpack_sync_modules', function($modules) {
    // Usuń niepotrzebne moduły synchronizacji
    unset($modules['posts']);
    unset($modules['comments']);
    return $modules;
});
```

## Konfiguracja Serwera

### 1. MySQL/MariaDB Optymalizacja
```ini
# my.cnf lub my.ini
[mysqld]
# Zwiększ cache dla InnoDB
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2

# Optymalizacja zapytań
query_cache_type = 1
query_cache_size = 128M
query_cache_limit = 2M

# Zwiększ limity
max_connections = 200
wait_timeout = 300
interactive_timeout = 300

# Optymalizacja tabel
table_open_cache = 2000
tmp_table_size = 128M
max_heap_table_size = 128M
```

### 2. PHP Konfiguracja
```ini
# php.ini
memory_limit = 512M
max_execution_time = 300
max_input_vars = 3000
post_max_size = 64M
upload_max_filesize = 64M

# OPcache
opcache.enable = 1
opcache.memory_consumption = 256
opcache.max_accelerated_files = 7963
opcache.revalidate_freq = 60
```

## Zadania Cron Systemowe

### 1. Zastąpienie WP-Cron
```bash
# Dodaj do crontab (crontab -e)
# Uruchamiaj co 5 minut
*/5 * * * * wget -q -O - https://firmup.eu/wp-cron.php?doing_wp_cron >/dev/null 2>&1

# Lub używając curl
*/5 * * * * curl -s https://firmup.eu/wp-cron.php?doing_wp_cron >/dev/null 2>&1
```

### 2. Automatyczne Czyszczenie Bazy
```bash
# Codzienne czyszczenie logów cron (dodaj do crontab)
0 2 * * * mysql -u username -p'password' eekai3_firmupeu -e "DELETE FROM wp_cron_logs WHERE executed < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY));"

# Tygodniowe czyszczenie transients
0 3 * * 0 mysql -u username -p'password' eekai3_firmupeu -e "DELETE FROM wp_options WHERE option_name LIKE '_transient_timeout_%' AND CAST(option_value AS UNSIGNED) < UNIX_TIMESTAMP();"

# Miesięczna optymalizacja tabel
0 4 1 * * mysql -u username -p'password' eekai3_firmupeu -e "OPTIMIZE TABLE wp_cron_logs, wp_options, wp_postmeta, wp_actionscheduler_actions;"
```

## Monitoring i Alerty

### 1. Skrypt Monitorowania
```bash
#!/bin/bash
# monitor_db.sh

DB_NAME="eekai3_firmupeu"
DB_USER="username"
DB_PASS="password"

# Sprawdź rozmiar tabeli wp_cron_logs
CRON_LOGS_COUNT=$(mysql -u $DB_USER -p$DB_PASS $DB_NAME -se "SELECT COUNT(*) FROM wp_cron_logs;")

if [ $CRON_LOGS_COUNT -gt 10000 ]; then
    echo "ALERT: wp_cron_logs ma $CRON_LOGS_COUNT rekordów - wymaga czyszczenia!"
    # Wyślij email lub powiadomienie
fi

# Sprawdź rozmiar bazy danych
DB_SIZE=$(mysql -u $DB_USER -p$DB_PASS information_schema -se "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' FROM information_schema.tables WHERE table_schema='$DB_NAME';")

echo "Rozmiar bazy danych: ${DB_SIZE}MB"
```

### 2. WordPress Health Check
```php
// Dodaj do functions.php
function firmup_database_health_check() {
    global $wpdb;
    
    // Sprawdź liczbę rekordów w problematycznych tabelach
    $cron_logs = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}cron_logs");
    $options_autoload = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}options WHERE autoload = 'yes'");
    
    if ($cron_logs > 10000) {
        add_action('admin_notices', function() use ($cron_logs) {
            echo '<div class="notice notice-error"><p>UWAGA: Tabela cron_logs ma ' . $cron_logs . ' rekordów. Wymaga czyszczenia!</p></div>';
        });
    }
    
    if ($options_autoload > 1000) {
        add_action('admin_notices', function() use ($options_autoload) {
            echo '<div class="notice notice-warning"><p>UWAGA: ' . $options_autoload . ' opcji z autoload=yes. Sprawdź optymalizację!</p></div>';
        });
    }
}
add_action('admin_init', 'firmup_database_health_check');
```

## Plan Wdrożenia

### Faza 1: Natychmiastowe (1-2 dni)
1. ✅ Backup bazy danych
2. ✅ Wyczyszczenie wp_cron_logs
3. ✅ Usunięcie wygasłych transients
4. ✅ Podstawowa optymalizacja

### Faza 2: Krótkoterminowe (1 tydzień)
1. 🔄 Konfiguracja system cron
2. 🔄 Optymalizacja pluginów
3. 🔄 Dodanie indeksów
4. 🔄 Konfiguracja cache

### Faza 3: Długoterminowe (1 miesiąc)
1. 📋 Monitoring automatyczny
2. 📋 Optymalizacja serwera
3. 📋 Regularne utrzymanie
4. 📋 Dokumentacja procesów

## Oczekiwane Rezultaty

### Krótkoterminowe (1-7 dni)
- ⚡ 50-70% poprawa czasu ładowania panelu admin
- ⚡ 30-50% poprawa czasu ładowania strony
- 📉 Zmniejszenie rozmiaru bazy o 60-80%
- 🔧 Stabilniejsze działanie cron jobs

### Długoterminowe (1-3 miesiące)
- 📈 Stała wysoka wydajność
- 🛡️ Automatyczne utrzymanie
- 📊 Monitoring i alerty
- 💾 Optymalne wykorzystanie zasobów

## Kontakt i Wsparcie

W przypadku problemów z wdrożeniem:
1. Sprawdź logi błędów serwera
2. Monitoruj wydajność po każdej zmianie
3. Przywróć backup w przypadku problemów
4. Skonsultuj się z administratorem serwera

---
*Dokument przygotowany: 2025-01-23*
*Wersja: 1.0*
*Status: Do wdrożenia*
